"use server";

import { OrderStatus, Role } from "@prisma/client";
import {
  notifyUsers,
  sendPushNotification,
} from "@udoy/libs/backend/push-service";
import { ActionError } from "@udoy/utils/app-error";
import { <PERSON>ieUtil } from "@udoy/utils/cookie-util";
import { getPrisma } from "@udoy/utils/db-utils";
import { revalidatePath } from "next/cache";

export async function assignDeliveryMan(orderId: number, userId: number) {
  try {
    const prisma = getPrisma();

    // Update the order with delivery man and change status to confirmed
    await prisma.order.update({
      where: { id: orderId },
      data: {
        deliveryManId: userId,
        status: OrderStatus.CONFIRMED,
      },
    });

    // Add a timeline entry for the status change
    await prisma.orderTimeline.create({
      data: {
        orderId,
        status: OrderStatus.CONFIRMED,
        note: `Delivery assigned to delivery person`,
      },
    });

    revalidatePath("/dashboard/orders");
    return true;
  } catch (error) {
    console.log(error);
    return ActionError("Failed to assign delivery man");
  }
}

export async function updateOrderStatus(
  orderId: number,
  status: OrderStatus,
  note?: string
) {
  try {
    const prisma = getPrisma();

    // Update the order status with timestamp
    const order = await prisma.order.update({
      where: { id: orderId },
      data: {
        status,
      },
    });

    // Create a timeline entry for this status change
    await prisma.orderTimeline.create({
      data: {
        orderId,
        status,
        note: note || getDefaultNoteForStatus(status),
      },
    });

    if (status === OrderStatus.CANCELLED) {
      await sendPushNotification(order.buyerId, {
        title: `আপনার অর্ডারটি বাতিল করা হয়েছে । অর্ডার নংঃ ${order.id}`,
        body: `অর্ডার বাতিলের কারণ: ${note}`,
        data: {
          url: `/orders`,
          tag: order.id,
        },
      });
    }

    // Revalidate both the orders list and the specific order page
    revalidatePath("/dashboard/orders");
    revalidatePath(`/dashboard/orders/${orderId}`);

    return true;
  } catch (error) {
    console.error(error);
    return ActionError("Failed to update order status");
  }
}

// Helper function to get default note for each status
function getDefaultNoteForStatus(status: OrderStatus): string {
  switch (status) {
    case OrderStatus.PENDING:
      return "Order placed by customer";
    case OrderStatus.CONFIRMED:
      return "Order confirmed and processing started";
    case OrderStatus.SHIPPING:
      return "Order out for delivery";
    case OrderStatus.DELIVERED:
      return "Order successfully delivered";
    case OrderStatus.CANCELLED:
      return "Order cancelled";
    case OrderStatus.RETURNED:
      return "Order returned by customer";
    default:
      return `Status changed to ${status}`;
  }
}

export async function deleteOrder(orderId: number) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }
    const prisma = getPrisma();

    const superAdmin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
    });

    if (!superAdmin) {
      return ActionError("Unauthorized");
    }

    const order = await prisma.order.findUnique({
      where: { id: orderId },
    });

    if (!order) {
      return ActionError("Order not found");
    }

    await prisma.order.delete({
      where: { id: orderId },
    });

    revalidatePath("/dashboard/orders");
    return true;
  } catch (error) {
    console.log(error);
    return ActionError("Failed to delete order");
  }
}

export async function mergeOrders(orderIds: number[]) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();

    // Check admin permissions
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    // Validate input
    if (orderIds.length < 2) {
      return ActionError("At least 2 orders are required for merging");
    }

    // Fetch all orders with their items
    const orders = await prisma.order.findMany({
      where: {
        id: { in: orderIds },
      },
      include: {
        orderItems: true,
        buyer: true,
        address: true,
      },
      orderBy: {
        id: 'asc', // Primary order will be the first one (lowest ID)
      },
    });

    if (orders.length !== orderIds.length) {
      return ActionError("Some orders were not found");
    }

    // The first order (lowest ID) becomes the primary order
    const primaryOrder = orders[0];
    const ordersToMerge = orders.slice(1);

    // Collect all order items from orders to be merged
    const allOrderItems = ordersToMerge.flatMap(order => order.orderItems);

    // Calculate new totals
    const additionalSubTotal = ordersToMerge.reduce((sum, order) => sum + order.subTotal, 0);
    const additionalShipping = ordersToMerge.reduce((sum, order) => sum + order.shipping, 0);
    const additionalDiscount = ordersToMerge.reduce((sum, order) => sum + order.discount, 0);
    const additionalProfit = ordersToMerge.reduce((sum, order) => sum + order.profit, 0);

    // Perform the merge in a transaction
    await prisma.$transaction(async (tx) => {
      // Update order items to point to the primary order
      if (allOrderItems.length > 0) {
        await tx.orderItem.updateMany({
          where: {
            id: { in: allOrderItems.map(item => item.id) },
          },
          data: {
            orderId: primaryOrder.id,
          },
        });
      }

      // Update primary order totals
      await tx.order.update({
        where: { id: primaryOrder.id },
        data: {
          subTotal: primaryOrder.subTotal + additionalSubTotal,
          shipping: primaryOrder.shipping + additionalShipping,
          discount: primaryOrder.discount + additionalDiscount,
          profit: primaryOrder.profit + additionalProfit,
        },
      });

      // Add timeline entry for the merge
      await tx.orderTimeline.create({
        data: {
          orderId: primaryOrder.id,
          status: primaryOrder.status,
          note: `Merged with orders: ${ordersToMerge.map(o => `#${o.id}`).join(', ')}`,
        },
      });

      // Delete the merged orders (this will cascade delete their timelines)
      await tx.order.deleteMany({
        where: {
          id: { in: ordersToMerge.map(order => order.id) },
        },
      });
    });

    revalidatePath("/dashboard/orders");
    return { success: true, primaryOrderId: primaryOrder.id };
  } catch (error) {
    console.log(error);
    return ActionError("Failed to merge orders");
  }
}

export async function updateOrderItemQuantity({
  itemId,
  orderId,
  quantity,
}: {
  itemId: number;
  orderId: number;
  quantity: number;
}) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }
    const prisma = getPrisma();

    const superAdmin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
    });

    if (!superAdmin) {
      return ActionError("Unauthorized");
    }

    if (isNaN(itemId) || isNaN(orderId) || isNaN(quantity) || quantity <= 0) {
      return ActionError("Invalid input data");
    }

    // Update the order item quantity
    await prisma.orderItem.update({
      where: { id: itemId },
      data: { quantity },
    });

    // Recalculate order subtotal and profit
    const orderItems = await prisma.orderItem.findMany({
      where: { orderId },
    });

    const subTotal = orderItems.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    const totalProfit = orderItems.reduce(
      (sum, item) => sum + (item.price - item.sourcePrice) * item.quantity,
      0
    );

    await prisma.order.update({
      where: { id: orderId },
      data: {
        subTotal,
        profit: totalProfit,
      },
    });

    // Revalidate the order page
    revalidatePath(`/dashboard/orders/${orderId}`);

    return true;
  } catch (error) {
    console.error(error);
    return ActionError("Failed to update item quantity");
  }
}

export async function deleteOrderItem({
  itemId,
  orderId,
}: {
  itemId: number;
  orderId: number;
}) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    if (isNaN(itemId) || isNaN(orderId)) {
      return ActionError("Invalid input data");
    }

    const prisma = getPrisma();
    const superAdmin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
    });

    if (!superAdmin) {
      return ActionError("Unauthorized");
    }

    // Delete the order item
    await prisma.orderItem.delete({
      where: { id: itemId },
    });

    // Recalculate order subtotal and total
    const orderItems = await prisma.orderItem.findMany({
      where: { orderId },
    });

    const subTotal = orderItems.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    const totalProfit = orderItems.reduce(
      (sum, item) => sum + (item.price - item.sourcePrice) * item.quantity,
      0
    );

    await prisma.order.update({
      where: { id: orderId },
      data: {
        subTotal,
        profit: totalProfit,
      },
    });

    // Revalidate the order page
    revalidatePath(`/dashboard/orders/${orderId}`);

    return true;
  } catch (error) {
    console.error(error);
    return ActionError("Failed to delete order item");
  }
}

// export async function addOrderItem({
//   orderId,
//   productId,
//   quantity,
// }: {
//   orderId: number;
//   productId: string;
//   quantity: number;
// }) {
//   try {
//     if (isNaN(orderId) || isNaN(quantity) || quantity < 1) {
//       return ActionError("Invalid input data");
//     }

//     const userId = await CookieUtil.userId();

//     if (!userId) {
//       return ActionError("Login to Continue");
//     }

//     const prisma = getPrisma();
//     const superAdmin = await prisma.user.findUnique({
//       where: {
//         id: userId,
//         role: {
//           in: [Role.SUPER_ADMIN, Role.ADMIN],
//         },
//       },
//     });

//     if (!superAdmin) {
//       return ActionError("Unauthorized");
//     }

//     // Add the order item
//     await prisma.orderItem.create({
//       data: {
//         orderId,
//         productId,
//         quantity,
//         price: (await prisma.product.findUnique({ where: { id: productId } }))!
//           .price,
//       },
//     });

//     // Recalculate order subtotal and total
//     const orderItems = await prisma.orderItem.findMany({
//       where: { orderId },
//     });

//     const subTotal = orderItems.reduce(
//       (sum, item) => sum + item.price * item.quantity,
//       0
//     );

//     await prisma.order.update({
//       where: { id: orderId },
//       data: {
//         subTotal,
//       },
//     });

//     // Revalidate the order page
//     revalidatePath(`/dashboard/orders/${orderId}`);

//     return true;
//   } catch (error) {
//     console.error(error);
//     return ActionError("Failed to add order item");
//   }
// }

export async function addOrderItemFromCart({ orderId }: { orderId: number }) {
  try {
    if (isNaN(orderId)) {
      return ActionError("Invalid input data");
    }

    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const admin = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
      include: {
        cart: true,
      },
    });

    if (!admin) {
      return ActionError("Unauthorized");
    }

    if (!admin.cart?.id) {
      return ActionError("Cart not found");
    }

    const cart = await prisma.cart.findUnique({
      where: {
        id: admin.cart.id,
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!cart) {
      return ActionError("Cart not found");
    }

    if (cart.items.length === 0) {
      return ActionError("Cart is empty");
    }

    const currentItems = await prisma.orderItem.findMany({
      where: { orderId },
    });

    const items = cart.items
      .map((item) => {
        return {
          price: item.product.price - item.product.discount,
          quantity: item.quantity,
          productId: item.productId,
          sourcePrice: item.product.sourcePrice,
        };
      })
      .filter((item) => {
        return !currentItems.find((i) => i.productId === item.productId);
      });

    await prisma.orderItem.createMany({
      data: items.map((item) => ({
        ...item,
        orderId,
      })),
    });

    // Recalculate order subtotal and total
    const orderItems = await prisma.orderItem.findMany({
      where: { orderId },
    });

    const subTotal = orderItems.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    await prisma.order.update({
      where: { id: orderId },
      data: {
        subTotal,
      },
    });

    await prisma.cartItem.deleteMany({
      where: {
        cartId: admin.cart.id,
      },
    });

    // Revalidate the order page
    revalidatePath(`/dashboard/orders/${orderId}`);

    return true;
  } catch (error) {
    console.error(error);
    return ActionError("Failed to add order item");
  }
}

// Toggle picked status for order item
export async function toggleOrderItemPicked({
  itemId,
  orderId,
}: {
  itemId: number;
  orderId: number;
}) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN, Role.MAINTAINER],
        },
      },
    });

    if (!user) {
      return ActionError("Unauthorized");
    }

    const orderItem = await prisma.orderItem.findUnique({
      where: { id: itemId },
    });

    if (!orderItem) {
      return ActionError("Order item not found");
    }

    const newPickedStatus = !orderItem.picked;

    await prisma.orderItem.update({
      where: { id: itemId },
      data: {
        picked: newPickedStatus,
        pickedAt: newPickedStatus ? new Date() : null,
        pickerId: newPickedStatus ? userId : null,
      },
    });

    revalidatePath(`/dashboard/orders/${orderId}`);
    return true;
  } catch (error) {
    console.error(error);
    return ActionError("Failed to update picked status");
  }
}



// Update order item source price
export async function updateOrderItemSourcePrice({
  itemId,
  orderId,
  sourcePrice,
  updateProductPrice = false,
}: {
  itemId: number;
  orderId: number;
  sourcePrice: number;
  updateProductPrice?: boolean;
}) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
    });

    if (!user) {
      return ActionError("Unauthorized");
    }

    if (isNaN(itemId) || isNaN(orderId) || isNaN(sourcePrice) || sourcePrice <= 0) {
      return ActionError("Invalid input data");
    }

    const orderItem = await prisma.orderItem.findUnique({
      where: { id: itemId },
      include: { product: true },
    });

    if (!orderItem) {
      return ActionError("Order item not found");
    }

    // Update order item source price
    await prisma.orderItem.update({
      where: { id: itemId },
      data: {
        sourcePrice,
      },
    });

    // Update product source price if requested
    if (updateProductPrice) {
      await prisma.product.update({
        where: { id: orderItem.productId },
        data: {
          sourcePrice,
        },
      });
    }

    // Recalculate order profit
    const orderItems = await prisma.orderItem.findMany({
      where: { orderId },
    });

    const totalProfit = orderItems.reduce(
      (sum, item) => sum + (item.price - (item.id === itemId ? sourcePrice : item.sourcePrice)) * item.quantity,
      0
    );

    await prisma.order.update({
      where: { id: orderId },
      data: {
        profit: totalProfit,
      },
    });

    revalidatePath(`/dashboard/orders/${orderId}`);
    return true;
  } catch (error) {
    console.error(error);
    return ActionError("Failed to update source price");
  }
}

// Update order item selling price
export async function updateOrderItemSellingPrice({
  itemId,
  orderId,
  sellingPrice,
  updateProductPrice = false,
}: {
  itemId: number;
  orderId: number;
  sellingPrice: number;
  updateProductPrice?: boolean;
}) {
  try {
    const userId = await CookieUtil.userId();

    if (!userId) {
      return ActionError("Login to Continue");
    }

    const prisma = getPrisma();
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
        role: {
          in: [Role.SUPER_ADMIN, Role.ADMIN],
        },
      },
    });

    if (!user) {
      return ActionError("Unauthorized");
    }

    if (isNaN(itemId) || isNaN(orderId) || isNaN(sellingPrice) || sellingPrice <= 0) {
      return ActionError("Invalid input data");
    }

    const orderItem = await prisma.orderItem.findUnique({
      where: { id: itemId },
      include: { product: true },
    });

    if (!orderItem) {
      return ActionError("Order item not found");
    }

    // Update order item selling price
    await prisma.orderItem.update({
      where: { id: itemId },
      data: {
        price: sellingPrice,
      },
    });

    // Update product selling price if requested
    if (updateProductPrice) {
      await prisma.product.update({
        where: { id: orderItem.productId },
        data: {
          price: sellingPrice,
        },
      });
    }

    // Recalculate order subtotal and profit
    const orderItems = await prisma.orderItem.findMany({
      where: { orderId },
    });

    const subTotal = orderItems.reduce(
      (sum, item) => sum + (item.id === itemId ? sellingPrice : item.price) * item.quantity,
      0
    );

    const totalProfit = orderItems.reduce(
      (sum, item) => sum + ((item.id === itemId ? sellingPrice : item.price) - item.sourcePrice) * item.quantity,
      0
    );

    await prisma.order.update({
      where: { id: orderId },
      data: {
        subTotal,
        profit: totalProfit,
      },
    });

    revalidatePath(`/dashboard/orders/${orderId}`);
    return true;
  } catch (error) {
    console.error(error);
    return ActionError("Failed to update selling price");
  }
}
