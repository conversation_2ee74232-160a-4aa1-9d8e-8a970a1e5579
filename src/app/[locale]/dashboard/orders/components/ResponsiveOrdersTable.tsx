"use client";

import { useIsMobile } from "@udoy/hooks/use-mobile";
import Link from "next/link";
import { <PERSON><PERSON> } from "@udoy/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Badge } from "@udoy/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogTrigger,
} from "@udoy/components/ui/alert-dialog";
import { MoreHorizontal } from "lucide-react";
import { cn } from "@udoy/utils/shadcn";
import { OrderFullInterface } from "@udoy/utils/types";
import { OrderStatus } from "@prisma/client";
import AssignDeliveryMan from "./AssignDeliveryMan";
import DeleteOrder from "./DeleteOrder";
import { OrderCard } from "./OrderCard";
import { Fragment } from "react";

// Helper function to get status badge variant
function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "DELIVERED":
      return "default";
    case "PROCESSING":
      return "secondary";
    case "PENDING":
      return "outline";
    case "CANCELLED":
      return "destructive";
    case "DELIVERY":
      return "warning";
    case "RETURNED":
      return "destructive";
    default:
      return "outline";
  }
}

// Helper function to format date
function formatDate(date: Date) {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
}

interface ResponsiveOrdersTableProps {
  orders: OrderFullInterface[];
}

// Desktop table row component
function OrderRow({ order }: { order: OrderFullInterface }) {
  return (
    <TableRow>
      <TableCell className="font-medium">
        <Link
          href={`/dashboard/orders/${order.id}`}
          className="text-primary hover:underline"
        >
          #{order.id}
        </Link>
      </TableCell>
      <TableCell>
        <Link href={`/dashboard/customers/${order.buyer.id}`} className="group">
          <p className="font-medium group-hover:underline">
            {order.buyer.name}
          </p>
          <p className="text-xs text-muted-foreground">{order.buyer.email}</p>
        </Link>
      </TableCell>
      <TableCell>{formatDate(order.createdAt)}</TableCell>
      <TableCell>
        <div>
          <p className="font-medium">
            {order.deliveryMan?.name ?? "Not Assigned"}
          </p>
          <p className="text-xs text-muted-foreground">
            {order.deliveryMan?.email ?? ""}
          </p>
        </div>
      </TableCell>
      <TableCell className="text-right">{order.orderItems.length}</TableCell>
      <TableCell className="text-right font-medium">
        ৳ {(order.subTotal + order.shipping).toLocaleString()}
      </TableCell>
      <TableCell>
        <Badge
          variant={getStatusBadgeVariant(order.status) as any}
          className={cn(
            order.status === OrderStatus.DELIVERED &&
              "bg-green-700 hover:bg-green-600"
          )}
        >
          {order.status.replace("_", " ")}
        </Badge>
      </TableCell>
      <TableCell className="text-right">
        <AlertDialog>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>

              <DropdownMenuItem asChild>
                <Link href={`/dashboard/orders/${order.id}`}>View details</Link>
              </DropdownMenuItem>
              <AssignDeliveryMan order={order} />
              <Link href={`/api/orders/${order.id}/invoice`} target="_blank">
                <DropdownMenuItem>Get Invoice</DropdownMenuItem>
              </Link>
              <DropdownMenuSeparator />
              <AlertDialogTrigger asChild>
                <DropdownMenuItem className="bg-destructive text-destructive-foreground mt-2">
                  Delete Order
                </DropdownMenuItem>
              </AlertDialogTrigger>
            </DropdownMenuContent>
          </DropdownMenu>
          <DeleteOrder id={order.id} />
        </AlertDialog>
      </TableCell>
    </TableRow>
  );
}

export function ResponsiveOrdersTable({ orders }: ResponsiveOrdersTableProps) {
  // Mobile card layout

  // Desktop table layout
  return (
    <Fragment>
      <div className="space-y-4 sm:hidden">
        {orders.length > 0 ? (
          orders.map((order) => <OrderCard key={order.id} order={order} />)
        ) : (
          <div className="text-center py-12 text-muted-foreground">
            <div className="text-lg font-medium mb-2">No orders found</div>
            <div className="text-sm">
              Try adjusting your filters or search terms
            </div>
          </div>
        )}
      </div>
      <div className="rounded-md border hidden sm:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]">Order ID</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Delivery Man</TableHead>
              <TableHead className="text-right">Items</TableHead>
              <TableHead className="text-right">Total</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orders.length > 0 ? (
              orders.map((order) => <OrderRow key={order.id} order={order} />)
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  No orders found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </Fragment>
  );
}
