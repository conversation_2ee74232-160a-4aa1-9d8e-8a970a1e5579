"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@udoy/components/ui/dialog";
import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import { OrderFullInterface } from "@udoy/utils/types";
import { AlertTriangle, User, MapPin, Calendar } from "lucide-react";
import { mergeOrders } from "../actions";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface MergeOrdersDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orders: OrderFullInterface[];
  onMergeComplete: () => void;
}

export default function MergeOrdersDialog({
  open,
  onOpenChange,
  orders,
  onMergeComplete,
}: MergeOrdersDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // Check if orders are from different users
  const uniqueUsers = new Set(orders.map(order => order.buyerId));
  const hasDifferentUsers = uniqueUsers.size > 1;

  // Get the primary order (first selected order)
  const primaryOrder = orders[0];
  const ordersToMerge = orders.slice(1);

  const handleMerge = async () => {
    if (orders.length < 2) return;

    setIsLoading(true);
    try {
      const result = await mergeOrders(orders.map(order => order.id));
      
      if (result && typeof result === 'object' && 'message' in result) {
        toast.error(result.message as string);
      } else {
        toast.success(`Successfully merged ${orders.length} orders into order #${primaryOrder.id}`);
        onMergeComplete();
        router.refresh();
      }
    } catch (error) {
      toast.error("Failed to merge orders");
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {hasDifferentUsers && <AlertTriangle className="h-5 w-5 text-amber-500" />}
            Merge Orders Confirmation
          </DialogTitle>
          <DialogDescription>
            {hasDifferentUsers ? (
              <div className="text-amber-600 font-medium">
                ⚠️ Warning: You are merging orders from different customers. This action cannot be undone.
              </div>
            ) : (
              "You are about to merge the selected orders. This action cannot be undone."
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Primary Order */}
          <div className="border rounded-lg p-4 bg-green-50 border-green-200">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-green-800">Primary Order (Will Remain)</h3>
              <Badge variant="outline" className="bg-green-100 text-green-800">
                Order #{primaryOrder.id}
              </Badge>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>{primaryOrder.buyer.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(primaryOrder.createdAt)}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                <span>{primaryOrder.address.name}</span>
              </div>
              <div>
                <span className="font-medium">
                  ৳ {(primaryOrder.subTotal + primaryOrder.shipping).toLocaleString()}
                </span>
                <span className="text-muted-foreground ml-1">
                  ({primaryOrder.orderItems.length} items)
                </span>
              </div>
            </div>
          </div>

          {/* Orders to be merged */}
          <div className="space-y-2">
            <h3 className="font-semibold text-red-800">Orders to be Merged (Will be Deleted)</h3>
            {ordersToMerge.map((order) => (
              <div key={order.id} className="border rounded-lg p-4 bg-red-50 border-red-200">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline" className="bg-red-100 text-red-800">
                    Order #{order.id}
                  </Badge>
                  {order.buyerId !== primaryOrder.buyerId && (
                    <Badge variant="destructive" className="text-xs">
                      Different Customer
                    </Badge>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>{order.buyer.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(order.createdAt)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>{order.address.name}</span>
                  </div>
                  <div>
                    <span className="font-medium">
                      ৳ {(order.subTotal + order.shipping).toLocaleString()}
                    </span>
                    <span className="text-muted-foreground ml-1">
                      ({order.orderItems.length} items)
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Summary */}
          <div className="border-t pt-4">
            <div className="flex justify-between items-center font-semibold">
              <span>Total Items After Merge:</span>
              <span>{orders.reduce((sum, order) => sum + order.orderItems.length, 0)} items</span>
            </div>
            <div className="flex justify-between items-center font-semibold">
              <span>Total Value After Merge:</span>
              <span>
                ৳ {orders.reduce((sum, order) => sum + order.subTotal + order.shipping, 0).toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleMerge} 
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700"
          >
            {isLoading ? "Merging..." : `Merge ${orders.length} Orders`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
