import {
  Category,
  Product,
  ProductAvailability,
  ProductImage,
  QuantityUnit,
} from "@prisma/client";
import { UnitUtil } from "@udoy/utils/product-unit";
import Image from "next/image";
import AddToCart from "./AddToCart";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { getLocale } from "next-intl/server";
import { localeNumber } from "@udoy/utils";
import { useLocale } from "next-intl";
import badgeImage from "@udoy/assets/images/badge.png";
import Locale from "@udoy/components/Locale/Client";
import Hide from "@udoy/components/Hide";
import { Link } from "@udoy/i18n/navigation";
import { AdminEditLink } from "./AdminEditLink";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@udoy/components/ui/popover";
import { Home, TrendingUp } from "lucide-react";

function Pricing({ price, discount }: { price: number; discount: number }) {
  const locale = useLocale();

  if (discount === 0) {
    return (
      <span className="font-semibold">
        ৳ {localeNumber(price - discount, locale)}
      </span>
    );
  }

  return (
    <div className="relative">
      <span className="font-semibold line-through text-muted-foreground text-xs absolute block min-w-max left-full -top-1">
        ৳ {localeNumber(price, locale)}
      </span>
      <span className="font-semibold leading-none">
        ৳ {localeNumber(price - discount, locale)}
      </span>
    </div>
  );
}

function ProductItem({
  product,
  locale,
}: {
  product: Product & {
    images: ProductImage[];
    unit: QuantityUnit;
    category: { slug: string } | null;
    availability: ProductAvailability[];
  };
  locale: string;
}) {
  const isBangla = locale === "bn";

  return (
    <Card className="flex flex-col relative">
      <Hide open={product.discount > 0}>
        <div className="max-w-max text-[10px] text-center absolute font-semibold left-2 z-10">
          <div className="flex flex-col bg-[#df0000] text-white px-1 pt-1 ">
            <span className="font-bold text-xs ">
              ৳{product.discount.toLocaleString(locale)}
            </span>
            <span className="leading-[1]">
              <Locale bn="ছাড়">OFF</Locale>
            </span>
          </div>
          <div
            className="h-4"
            style={{
              backgroundImage: `url(${badgeImage.src})`,
              backgroundSize: "20% 80%",
              backgroundRepeat: "repeat-x",
              backgroundPosition: "top",
            }}
          ></div>
        </div>
      </Hide>

      {/* Product Feature Icons */}
      <div className="absolute top-2 right-2 z-20 flex flex-col gap-1 justify-center items-center">
        <AdminEditLink productId={product.id} className="h-8 w-8 p-0" />
        <Hide open={(product as any).isHomeMade}>
          <Popover>
            <PopoverTrigger asChild>
              <button className="bg-white border rounded-full w-6 h-6 shadow-md grid place-items-center hover:bg-gray-50 transition-colors">
                <Home className="h-3 w-3" />
              </button>
            </PopoverTrigger>
            <PopoverContent side="right" className="w-64 p-3">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Home className="h-4 w-4" />
                  <span className="font-medium text-sm">
                    <Locale bn="ঘরে তৈরি পণ্য">Home Made Product</Locale>
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">
                  <Locale bn="এই পণ্যটি ঘরে তৈরি করা হয় এবং প্রস্তুত করতে স্বাভাবিকের চেয়ে বেশি সময় লাগতে পারে।">
                    This product is home-made and may take longer than usual to
                    prepare.
                  </Locale>
                </p>
              </div>
            </PopoverContent>
          </Popover>
        </Hide>

        <Hide open={(product as any).isHigherPriced}>
          <Popover>
            <PopoverTrigger asChild>
              <button className="bg-white border rounded-full w-6 h-6 shadow-md grid place-items-center hover:bg-gray-50 transition-colors">
                <TrendingUp className="h-3 w-3 text-red-700" />
              </button>
            </PopoverTrigger>
            <PopoverContent side="right" className="w-64 p-3">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-red-700" />
                  <span className="font-medium text-sm">
                    <Locale bn="বাড়তি মূল্য যুক্ত করা হয়েছে">
                      Higher Priced Product
                    </Locale>
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">
                  <Locale bn="এই প্রডাক্টটি বাজার দর থেকে বেশি মূল্যে বিক্রি করা হচ্ছে।">
                    This product is sold at a higher price than the market
                    price.
                  </Locale>
                </p>
              </div>
            </PopoverContent>
          </Popover>
        </Hide>
      </div>

      {/* Admin Edit Button */}
      {/* <div className="absolute top-2 right-2 z-20">
      </div> */}
      <Link href={{ pathname: `/${product.category?.slug}/${product.slug}` }}>
        <CardHeader className="p-0">
          <Image
            src={product.images[0]?.url}
            width={400}
            height={400}
            alt={product.name}
            className="object-cover w-full rounded rounded-b-none"
          />
        </CardHeader>
      </Link>
      <CardContent className="p-2 flex-1 flex flex-col">
        <CardTitle className="text-center text-sm line-clamp-2 mt-3">
          {isBangla ? product.nam || product.name : product.name}
        </CardTitle>

        <div className="flex-1"></div>
        <div className="flex justify-between items-center mt-3 text-lg ">
          <Pricing discount={product.discount} price={product.price} />
          <span className="text-sm">
            {UnitUtil.getAmountUnit(product.amount, product.unit, locale)}
          </span>
        </div>
      </CardContent>
      <CardFooter className="p-2 pt-0">
        <AddToCart product={product} />
      </CardFooter>
    </Card>
  );
}

export default ProductItem;
