"use client";

import { Product, ProductAvailability } from "@prisma/client";
import { manageCart } from "@udoy/actions/cart";
import Button from "@udoy/components/Button";
import Hide from "@udoy/components/Hide";
import useCartUtils from "@udoy/hooks/useCartUtils";
import useProductAvailability from "@udoy/hooks/useProductAvailability";
import useStatus from "@udoy/hooks/useToastUtil";
import useUser from "@udoy/hooks/useUser";
import { store } from "@udoy/state";
import { localeNumber } from "@udoy/utils";
import { withError } from "@udoy/utils/app-error";
import { useSelector } from "@xstate/store/react";
import { Clock, Minus, Plus } from "lucide-react";
import { useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import { Fragment, useState, useTransition } from "react";
import { toast } from "react-toastify";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@udoy/components/ui/alert-dialog";
import Locale from "@udoy/components/Locale/Client";

function CartActions({
  product,
}: {
  product: Product & { availability: ProductAvailability[] };
}) {
  const locale = useLocale();
  const count = useSelector(
    store,
    (state) => state.context.cartItems[product.id]?.count
  );
  const isBangla = locale === "bn";
  const [alert, setAlert] = useState(false);
  const minOrder = (product as any)?.minOrder || 1;

  const { handleRemoveFromCart, handleAddToCart } = useCartUtils(product);
  const { isAvailable, title, message } = useProductAvailability(product);

  function handleClick() {
    if (isAvailable) {
      return handleAddToCart();
    }
    setAlert(true);
  }

  return (
    <Fragment>
      <div className="w-full space-y-1">
        <Hide
          open={!!count}
          fallback={
            <Button
              label={locale === "en" ? "Add to cart" : "কার্টে যুক্ত করুন"}
              className="w-full"
              onClick={handleClick}
            >
              {!isAvailable && <Clock size="15" className="-mt-0.5 mr-1" />}
            </Button>
          }
        >
          <div className="flex w-full">
            <Button
              className="rounded-r-none border-r max-[380px]:px-1.5"
              onClick={handleRemoveFromCart}
            >
              <Minus />
            </Button>
            <Button className="flex-1 rounded-none">
              <span className="hidden sm:block">
                {isBangla
                  ? `ব্যাগে ${count?.toLocaleString(locale)} টি`
                  : `${count?.toLocaleString(locale)} In Cart`}
              </span>
              <span className="sm:hidden">{count?.toLocaleString(locale)}</span>
            </Button>
            <Button
              className="rounded-l-none border-l max-[380px]:px-1.5"
              onClick={handleAddToCart}
            >
              <Plus />
            </Button>
          </div>
        </Hide>

        {minOrder > 1 && (
          <div className="text-xs text-muted-foreground text-center">
            {isBangla
              ? `সর্বনিম্ন ${minOrder.toLocaleString(locale)} টি`
              : `Min ${minOrder.toLocaleString(locale)} pcs`
            }
          </div>
        )}
      </div>
      <AlertDialog open={alert} onOpenChange={setAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              <Locale bn={title}>Product is currently Unavailable</Locale>
            </AlertDialogTitle>
            <AlertDialogDescription>{message}</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col gap-2 !space-x-0">
            <AlertDialogCancel className="mt-0">
              <Locale bn="বাতিল করুন">Cancel</Locale>
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleAddToCart}>
              <Locale bn="কার্টে যুক্ত করুন">Add To Cart</Locale>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Fragment>
  );
}

export default CartActions;
