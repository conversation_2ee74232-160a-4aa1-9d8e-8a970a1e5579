"use client";

import { Product } from "@prisma/client";
import Hide from "@udoy/components/Hide";
import { Button } from "@udoy/components/ui/button";
import useCartUtils from "@udoy/hooks/useCartUtils";
import { store } from "@udoy/state";
import { IS_SERVER } from "@udoy/utils/is-server";
import { useSelector } from "@xstate/store/react";
import { Heart, Share2, ShoppingCart } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";
import { useCopyToClipboard } from "react-use";

function ProductActions({ product }: { product: Product }) {
  const quantity = useSelector(
    store,
    (state) => state.context.cartItems[product.id]?.count || 0
  );

  const router = useRouter();
  const { handleAddToCart, handleRemoveFromCart } = useCartUtils(product);
  const [state, copy] = useCopyToClipboard();
  const minOrder = (product as any)?.minOrder || 1;

  function handleBuyNow() {
    quantity === 0 && handleAddToCart();
    router.push("/checkout");
  }

  return (
    <div className="space-y-6">
      <div className="space-y-3">
        <label className="text-sm font-medium text-gray-700">Quantity</label>
        <div className="flex items-center gap-3">
          <div className="flex items-center border rounded-lg">
            <button
              onClick={handleRemoveFromCart}
              disabled={quantity <= 0}
              className="px-3 py-2 text-gray-600 hover:bg-gray-100 disabled:opacity-50"
            >
              -
            </button>
            <span className="px-4 py-2 font-medium">{quantity}</span>
            <button
              onClick={handleAddToCart}
              disabled={quantity >= product.maxOrder}
              className="px-3 py-2 text-gray-600 hover:bg-gray-100 disabled:opacity-50"
            >
              +
            </button>
          </div>
          <div className="text-sm text-gray-600 space-y-1">
            {minOrder > 1 && (
              <div>Min {minOrder} per order</div>
            )}
            <div>Max {product.maxOrder} per order</div>
          </div>
        </div>
      </div>

      {/* Total Price */}
      <div className="bg-gray-100 rounded-lg p-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-600">Subtotal ({quantity} items):</span>
          <span className="text-xl font-bold">
            ৳{(product.price - product.discount) * quantity}
          </span>
        </div>
        {quantity * product.discount > 0 && (
          <div className="flex justify-between items-center text-green-600">
            <span>You save:</span>
            <span className="font-medium">৳{quantity * product.discount}</span>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="space-y-3">
        <Button
          onClick={handleBuyNow}
          className="w-full py-3 text-lg font-medium"
          size="lg"
        >
          Buy Now
        </Button>
        <Button
          onClick={handleAddToCart}
          variant="outline"
          className="w-full py-3 text-lg font-medium"
          size="lg"
          disabled={quantity >= product.maxOrder}
        >
          <ShoppingCart className="w-5 h-5 mr-2" />
          Add to Cart
        </Button>

        <div className="flex gap-2">
          {/* <Button
            variant="outline"
            size="sm"
            //   onClick={() => setIsWishlisted(!isWishlisted)}
            //   className={isWishlisted ? "text-red-500 border-red-500" : ""}
          >
            <Heart
              className={`w-4 h-4 mr-2 ${
                //   isWishlisted ? "fill-current" : ""
                ""
              }`}
            />
            Wishlist
          </Button> */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => copy(window.location.href)}
          >
            <Hide
              open={!IS_SERVER() && state.value !== window.location.href}
              fallback={<span>Copied!</span>}
            >
              <Share2 className="w-4 h-4 mr-2" />
              <span>Share</span>
            </Hide>
          </Button>
        </div>
      </div>
    </div>
  );
}

export default ProductActions;
