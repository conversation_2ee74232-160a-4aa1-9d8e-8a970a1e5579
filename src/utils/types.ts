import {
  Category,
  Prisma,
  Product,
  ProductImage,
  QuantityUnit,
} from "@prisma/client";
import { routing } from "@udoy/i18n/routing";
// import {formats} from '@udoy/i18n/request';
import messages from "../messages/en.json";

export interface PageProps<T = {}, K = {}> {
  params: Promise<T>;
  searchParams: Promise<K>;
}

export interface ProductDetails extends Product {
  unit: QuantityUnit;
  images: ProductImage[];
}

declare module "next-intl" {
  interface AppConfig {
    Locale: (typeof routing.locales)[number];
    Messages: typeof messages;
    Formats: any;
  }
}

export type AddressWithZone = Prisma.AddressGetPayload<{
  include: { zone: true };
}>;

export type ZoneWithSubzones = Prisma.DeliveryZoneGetPayload<{
  include: {
    subZones: true;
  };
}>;

export type OrderWithItems = Prisma.OrderGetPayload<{
  include: {
    orderItems: {
      include: {
        product: {
          include: {
            images: true;
          };
        };
      };
    };
  };
}>;

export type OrderFullInterface = Prisma.OrderGetPayload<{
  include: {
    buyer: true;
    address: true;
    deliveryMan: true;
    orderItems: {
      include: {
        product: {
          include: {
            images: true;
          };
        };
        picker: true;
      };
    };
  };
}>;

export type DiscordOrderMessage = Prisma.OrderGetPayload<{
  include: {
    buyer: true;
    address: {
      include: {
        zone: true;
      };
    };
    orderItems: {
      include: {
        product: true;
      };
    };
  };
}>;

export type OrderItemWithProduct = Prisma.OrderItemGetPayload<{
  include: {
    product: {
      include: {
        images: true;
      };
    };
  };
}>;

export type CartItemWithProduct = Prisma.CartItemGetPayload<{
  include: {
    product: true;
  };
}>;

export type ProductWithCategory = Prisma.ProductGetPayload<{
  include: {
    category: true;
    images: true;
    unit: true;
  };
}>;

export type CategoryWithSubcategories = Prisma.CategoryGetPayload<{
  include: {
    subCategories: true;
  };
}>;
